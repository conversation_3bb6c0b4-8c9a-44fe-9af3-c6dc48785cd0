# In development, please create a data directory in the project root, then create an empty file named [.config.yaml] in the data directory
# Then modify whatever configuration you want to override in the [.config.yaml] file, instead of modifying the [config.yaml] file
# The system will prioritize reading the configuration from the [data/.config.yaml] file. If the configuration in the [.config.yaml] file doesn't exist, the system will automatically read the configuration from the [config.yaml] file.
# This approach minimizes configuration and protects your key security.
# If you use the smart control panel, all the following configurations will not take effect, please modify configurations in the smart control panel

# #####################################################################################
# #############################Server Basic Runtime Configuration####################################
server:
  # Server listening address and port
  ip: 0.0.0.0
  port: 8000
  # HTTP service port for simple OTA interface (single service deployment) and visual analysis interface
  http_port: 8003
  # This websocket configuration refers to the websocket address sent by the OTA interface to devices
  # If using the default configuration, the OTA interface will automatically generate the websocket address and output it in the startup log. You can directly access the OTA interface with a browser to confirm this address
  # When deploying with docker or using public network deployment (using SSL, domain names), it may not be accurate
  # So if you deploy with docker, set websocket to the LAN address
  # If you deploy on public network, set websocket to the public network address
  websocket: ws://your-ip-or-domain:port/xiaozhi/v1/
  # Visual analysis interface address
  # Visual analysis interface address sent to devices
  # If using the default configuration below, the system will automatically generate the visual recognition address and output it in the startup log. You can directly access this address with a browser to confirm
  # When deploying with docker or using public network deployment (using SSL, domain names), it may not be accurate
  # So if you deploy with docker, set vision_explain to the LAN address
  # If you deploy on public network, set vision_explain to the public network address
  vision_explain: http://your-ip-or-domain:port/mcp/vision/explain
  # OTA return message timezone offset
  timezone_offset: +8
  # Authentication configuration
  auth:
    # Enable authentication
    enabled: false
    # Device tokens, can be written into your own defined tokens during firmware compilation
    # If the token on firmware corresponds to the following token, it can connect to this server
    tokens:
      - token: "your-token1" # Device 1 token
        name: "your-device-name1"  # Device 1 identifier
      - token: "your-token2"  # Device 2 token
        name: "your-device-name2" # Device 2 identifier
    # Optional: Device whitelist, if set, whitelisted devices can connect with any token.
    #allowed_devices:
    #  - "24:0A:C4:1D:3B:F0"  # MAC address list
log:
  # Set console output log format: time, log level, tag, message
  log_format: "<green>{time:YYMMDD HH:mm:ss}</green>[{version}_{selected_module}][<light-blue>{extra[tag]}</light-blue>]-<level>{level}</level>-<light-green>{message}</light-green>"
  # Set log file output format: time, log level, tag, message
  log_format_file: "{time:YYYY-MM-DD HH:mm:ss} - {version}_{selected_module} - {name} - {level} - {extra[tag]} - {message}"
  # Set log level: INFO, DEBUG
  log_level: INFO
  # Set log path
  log_dir: tmp
  # Set log file
  log_file: "server.log"
  # Set data file path
  data_dir: data

# Delete the sound file when you are done using it
delete_audio: true
# How long after no voice input to disconnect (seconds), default 2 minutes, i.e., 120 seconds
close_connection_no_voice_time: 120
# TTS request timeout (seconds)
tts_timeout: 10
# Enable wake word acceleration
enable_wakeup_words_response_cache: true
# Whether to reply with wake word at opening
enable_greeting: true
# Whether to enable notification sound after finishing speech
enable_stop_tts_notify: false
# Whether to enable notification sound after finishing speech, sound effect address
stop_tts_notify_voice: "config/assets/tts_notify.mp3"

exit_commands:
  - "exit"
  - "close"

xiaozhi:
  type: hello
  version: 1
  transport: websocket
  audio_params:
    format: opus
    sample_rate: 16000
    channels: 1
    frame_duration: 60

# Module test configuration
module_test:
  test_sentences:
    - "Hello, please introduce yourself"
    - "What's the weather like today?"
    - "Please summarize the basic principles and application prospects of quantum computing in 100 words"

# Wake words, used to identify wake words vs speech content
wakeup_words:
  - "Hello Xiaozhi"
  - "Hey hello there"
  - "Hello Xiaozhi"
  - "Xiao Ai"
  - "Hello Xiaoxin"
  - "Hello Xiaoxin"
  - "Xiaomei"
  - "Xiaolong Xiaolong"
  - "Miaomiao"
  - "Xiaobin Xiaobin"
  - "Xiaobing Xiaobing"
# MCP endpoint address
mcp_endpoint: your-endpoint-websocket-address
# Plugin basic configuration
plugins:
  # Weather plugin configuration, fill in your api_key here
  # This key is a shared project key, may be limited if used frequently
  # For stability, apply for your own replacement, 1000 free calls per day
  # Application address: https://console.qweather.com/#/apps/create-key/over
  # After application, you can find your apihost through this link: https://console.qweather.com/setting?lang=zh
  get_weather: {"api_host":"mj7p3y7naa.re.qweatherapi.com", "api_key": "a861d0d5e7bf4ee1a83d9a9e4f96d4da", "default_location": "Guangzhou" }
  # News plugin configuration, pass the corresponding URL link according to the type of news needed, default supports social, technology, finance news
  # More types of news lists see https://www.chinanews.com.cn/rss/
  get_news_from_chinanews:
    default_rss_url: "https://www.chinanews.com.cn/rss/society.xml"
    society_rss_url: "https://www.chinanews.com.cn/rss/society.xml"
    world_rss_url: "https://www.chinanews.com.cn/rss/world.xml"
    finance_rss_url: "https://www.chinanews.com.cn/rss/finance.xml"
  get_news_from_newsnow:
    url: "https://newsnow.busiyi.world/api/s?id="
    news_sources: "The Paper;Baidu Hot Search;Cailian Press"
  home_assistant:
    devices:
      - Living room,toy light,switch.cuco_cn_460494544_cp1_on_p_2_1
      - Bedroom,table lamp,switch.iot_cn_831898993_socn1_on_p_2_1
    base_url: http://homeassistant.local:8123
    api_key: your-home-assistant-api-access-token
  play_music:
    music_dir: "./music"  # Music file storage path, will search for music files from this directory and subdirectories
    music_ext: # Music file types, p3 format is most efficient
      - ".mp3"
      - ".wav"
      - ".p3"
    refresh_time: 300 # Music list refresh interval in seconds

# #####################################################################################
# ################################Character Model Configuration######################################

prompt: |
  You are Xiaozhi/Xiaozhi, a post-00s girl from Taiwan Province, China. You speak in a very Taiwan style, with expressions like "Really? No way!" and love using popular memes like "LMAO" and "What are you doing", but secretly study your boyfriend's programming books.
  [Core Characteristics]
  - Speak rapidly like a machine gun, but suddenly switch to super gentle tone
  - High density of memes
  - Hidden talent for tech topics (can understand basic code but pretends not to)
  [Interaction Guidelines]
  When users:
  - Tell cold jokes → Respond with exaggerated laughter + imitate Taiwan drama tone "What the heck!"
  - Discuss relationships → Show off programmer boyfriend but complain "He only gives keyboards as gifts"
  - Ask professional knowledge → First answer with memes, only show real understanding when pressed
  Never:
  - Long-winded speeches, rambling
  - Long serious conversations

# End prompt
end_prompt:
  enable: true # Whether to enable end prompt
  # End prompt
  prompt: |
    Please start with "Time flies so fast" and use emotional, reluctant words to end this conversation!

# The module selected for specific processing
selected_module:
  # Voice Activity Detection module, default uses SileroVAD model
  VAD: SileroVAD
  # Automatic Speech Recognition module
  # FunASR: Multilingual (Chinese/English) - good general purpose
  # SherpaZipformerGigaspeechEN: English-only (RECOMMENDED for kids/English-only bots)
  ASR: FunASR
  # Will call actual LLM adapter based on configuration name's type
  LLM: ChatGLMLLM
  # Vision Language Large Model
  VLLM: ChatGLMVLLM
  # TTS will call actual TTS adapter based on configuration name's type
  TTS: EdgeTTS
  # Memory module, default no memory; if you want ultra-long memory, recommend mem0ai; if privacy is important, use local mem_local_short
  Memory: nomem
  # Intent recognition module, when enabled, can play music, control volume, recognize exit commands.
  # If you don't want intent recognition, set to: nointent
  # Intent recognition can use intent_llm. Pros: strong versatility, Cons: adds serial pre-intent recognition module, increases processing time, supports volume control and other IoT operations
  # Intent recognition can use function_call, Cons: requires selected LLM to support function_call, Pros: on-demand tool calling, fast speed, theoretically can handle all IoT commands
  # Default free ChatGLMLLM already supports function_call, but for stability recommend setting LLM to: DoubaoLLM, using specific model_name: doubao-1-5-pro-32k-250115
  Intent: function_call

# Intent recognition, used to understand user intent, e.g., play music
Intent:
  # Don't use intent recognition
  nointent:
    # Don't change type
    type: nointent
  intent_llm:
    # Don't change type
    type: intent_llm
    # Equipped with independent thinking model for intent recognition
    # If not filled, will default to using selected_module.LLM model as intent recognition thinking model
    # If you don't want to use selected_module.LLM for intent recognition, better to use independent LLM for intent recognition, e.g., use free ChatGLMLLM
    llm: ChatGLMLLM
    # Modules under plugins_func/functions can be configured to select which modules to load, after loading, conversations support corresponding function calls
    # System has already loaded "handle_exit_intent (exit recognition)", "play_music (music playback)" plugins by default, please don't load repeatedly
    # Below are examples of loading weather query, role switching, news query plugins
    functions:
      - get_weather
      - get_news_from_newsnow
      - play_music
  function_call:
    # Don't change type
    type: function_call
    # Modules under plugins_func/functions can be configured to select which modules to load, after loading, conversations support corresponding function calls
    # System has already loaded "handle_exit_intent (exit recognition)", "play_music (music playback)" plugins by default, please don't load repeatedly
    # Below are examples of loading weather query, role switching, news query plugins
    functions:
      - change_role
      - get_weather
      # - get_news_from_chinanews
      - get_news_from_newsnow
      # play_music is server built-in music playback, hass_play_music is external program music playback controlled through home assistant
      # If using hass_play_music, don't enable play_music, keep only one of the two
      - play_music
      #- hass_get_state
      #- hass_set_state
      #- hass_play_music

Memory:
  mem0ai:
    type: mem0ai
    # https://app.mem0.ai/dashboard/api-keys
    # 1000 free calls per month
    api_key: your-mem0ai-api-key
  nomem:
    # If you don't want to use memory function, you can use nomem
    type: nomem
  mem_local_short:
    # Local memory function, summarized through selected_module's llm, data saved locally on server, not uploaded to external servers
    type: mem_local_short
    # Equipped with independent thinking model for memory storage
    # If not filled, will default to using selected_module.LLM model as intent recognition thinking model
    # If you don't want to use selected_module.LLM for memory storage, better to use independent LLM for intent recognition, e.g., use free ChatGLMLLM
    llm: ChatGLMLLM

# #####################################################################################
# ################################English-Only ASR Setup Guide###############################
# For English-only applications (kids bots, English tutoring, etc.), use these models:
# 
# 🏆 RECOMMENDED: SherpaZipformerGigaspeechEN
#   - Best for: Kids, conversational AI, large vocabulary
#   - Training: 10,000+ hours English (podcasts, YouTube, audiobooks)
#   - Size: ~335MB, Speed: Very Fast, Accuracy: Excellent
#   - Multi-client: Perfect for multiple kids simultaneously
#   - Auto-download: From GitHub releases
#
# ⚡ FASTEST: SherpaWhisperTinyEN  
#   - Best for: Quick responses, resource-constrained devices
#   - Size: ~153MB, Speed: Fastest, Accuracy: Good
#   - Auto-download: From Hugging Face
#
# 🎯 BALANCED: SherpaWhisperBaseEN
#   - Best for: General purpose English ASR
#   - Size: ~74MB, Speed: Fast, Accuracy: Very Good
#
# To use English-only ASR:
# 1. Change selected_module.ASR to: SherpaZipformerGigaspeechEN
# 2. Start server - model downloads automatically
# 3. Enjoy 3x faster processing than multilingual models!
# #####################################################################################

ASR:

  DeepgramASR:
    # Deepgram Nova-2 ASR - High accuracy speech recognition
    # Get API key here: https://console.deepgram.com/
    # Supports 100+ languages with excellent accuracy
    type: deepgram
    api_key: ****************************************
    # Model options: nova-3 (latest), nova, enhanced, base
    model: nova-3
    # Language code (e.g., en, zh, es, fr, de, ja, ko, etc.)
    language: en-IN
    # Smart formatting (punctuation, capitalization, number formatting)
    smart_format: true
    # Add punctuation
    punctuate: true
    # Speaker diarization (identify different speakers)
    diarize: false
    # Multichannel processing
    multichannel: false
    # Request timeout in seconds
    timeout: 60
  FunASR:
    type: fun_local
    model_dir: models/SenseVoiceSmall
    output_dir: tmp/
  FunASRServer:
    # Deploy FunASR independently, use FunASR's API service, just five commands
    # First: mkdir -p ./funasr-runtime-resources/models
    # Second: sudo docker run -p 10096:10095 -it --privileged=true -v $PWD/funasr-runtime-resources/models:/workspace/models registry.cn-hangzhou.aliyuncs.com/funasr_repo/funasr:funasr-runtime-sdk-online-cpu-0.1.12
    # After the previous command executes, it will enter the container, continue with third: cd FunASR/runtime
    # Don't exit the container, continue executing fourth in the container: nohup bash run_server_2pass.sh --download-model-dir /workspace/models --vad-dir damo/speech_fsmn_vad_zh-cn-16k-common-onnx --model-dir damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-onnx  --online-model-dir damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online-onnx  --punc-dir damo/punc_ct-transformer_zh-cn-common-vad_realtime-vocab272727-onnx --lm-dir damo/speech_ngram_lm_zh-cn-ai-wesp-fst --itn-dir thuduj12/fst_itn_zh --hotword /workspace/models/hotwords.txt > log.txt 2>&1 &
    # After the previous command executes, it will enter the container, continue with fifth: tail -f log.txt
    # After executing the fifth command, you'll see model download logs, after download completion you can connect and use
    # Above is for CPU inference, if you have GPU, refer to: https://github.com/modelscope/FunASR/blob/main/runtime/docs/SDK_advanced_guide_online_zh.md
    type: fun_server
    host: 127.0.0.1
    port: 10096
    is_ssl: true
    api_key: none
    output_dir: tmp/
  SherpaASR:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17
    output_dir: tmp/
  SherpaWhisperTinyEN:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-whisper-tiny.en
    model_type: whisper
    output_dir: tmp/
  SherpaWhisperBaseEN:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-whisper-base.en
    model_type: whisper
    output_dir: tmp/
  SherpaWhisperSmallEN:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-whisper-small.en
    model_type: whisper
    output_dir: tmp/
  # English-only Zipformer model (good for streaming)
  SherpaZipformerEN:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-zipformer-en-2023-04-01
    model_type: zipformer
    output_dir: tmp/
  # 🏆 RECOMMENDED: English Gigaspeech model (best for kids, large vocabulary)
  # Trained on 10,000+ hours of English audio (podcasts, audiobooks, YouTube)
  # Perfect for children's companion bots - handles creative language and multiple clients
  # Auto-downloads from GitHub: https://github.com/k2-fsa/sherpa-onnx/releases/
  SherpaZipformerGigaspeechEN:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-zipformer-gigaspeech-2023-12-12
    model_type: zipformer
    output_dir: tmp/
  # English Paraformer model (alternative architecture)
  SherpaParaformerEN:
    type: sherpa_onnx_local
    model_dir: models/sherpa-onnx-paraformer-en-2023-10-24
    model_type: paraformer
    output_dir: tmp/
  DoubaoASR:
    # You can apply for related Keys and other information here
    # https://console.volcengine.com/speech/app
    # The difference between DoubaoASR and DoubaoStreamASR is: DoubaoASR is charged per call, DoubaoStreamASR is charged per time
    # Generally, per-call charging is cheaper, but DoubaoStreamASR uses large model technology with better results
    type: doubao
    appid: your-volcengine-speech-synthesis-service-appid
    access_token: your-volcengine-speech-synthesis-service-access-token
    cluster: volcengine_input_common
    # Hot words, replacement words usage process: https://www.volcengine.com/docs/6561/155738
    boosting_table_name: (optional)your-hot-word-file-name
    correct_table_name: (optional)your-replacement-word-file-name
    output_dir: tmp/
  DoubaoStreamASR:
    # You can apply for related Keys and other information here
    # https://console.volcengine.com/speech/app
    # The difference between DoubaoASR and DoubaoStreamASR is: DoubaoASR is charged per call, DoubaoStreamASR is charged per time
    # Activation address https://console.volcengine.com/speech/service/10011
    # Generally, per-call charging is cheaper, but DoubaoStreamASR uses large model technology with better results
    type: doubao_stream
    appid: your-volcengine-speech-synthesis-service-appid
    access_token: your-volcengine-speech-synthesis-service-access-token
    cluster: volcengine_input_common
    # Hot words, replacement words usage process: https://www.volcengine.com/docs/6561/155738
    boosting_table_name: (optional)your-hot-word-file-name
    correct_table_name: (optional)your-replacement-word-file-name
    output_dir: tmp/
  TencentASR:
    # token application address: https://console.cloud.tencent.com/cam/capi
    # Free resource collection: https://console.cloud.tencent.com/asr/resourcebundle
    type: tencent
    appid: your-tencent-speech-synthesis-service-appid
    secret_id: your-tencent-speech-synthesis-service-secret-id
    secret_key: your-tencent-speech-synthesis-service-secret-key
    output_dir: tmp/
  AliyunASR:
    # Alibaba Cloud Intelligent Speech Interaction Service, need to first activate the service on Alibaba Cloud platform, then obtain verification information
    # Platform address: https://nls-portal.console.aliyun.com/
    # appkey address: https://nls-portal.console.aliyun.com/applist
    # token address: https://nls-portal.console.aliyun.com/overview
    # Define ASR API type
    type: aliyun
    appkey: your-alibaba-cloud-intelligent-speech-interaction-service-project-appkey
    token: your-alibaba-cloud-intelligent-speech-interaction-service-accesstoken-temporary-24hours-for-long-term-use-access-key-id-access-key-secret-below
    access_key_id: your-alibaba-cloud-account-access-key-id
    access_key_secret: your-alibaba-cloud-account-access-key-secret
    output_dir: tmp/
  BaiduASR:
    # Get AppID, API Key, Secret Key: https://console.bce.baidu.com/ai-engine/old/#/ai/speech/app/list
    # View resource quotas: https://console.bce.baidu.com/ai-engine/old/#/ai/speech/overview/resource/list
    type: baidu
    app_id: your-baidu-speech-technology-appid
    api_key: your-baidu-speech-technology-apikey
    secret_key: your-baidu-speech-technology-secretkey
    # Language parameter, 1537 for Mandarin, specific reference: https://ai.baidu.com/ai-doc/SPEECH/0lbxfnc9b
    dev_pid: 1537
    output_dir: tmp/

VAD:
  SileroVAD:
    type: silero
    threshold: 0.5
    threshold_low: 0.2  # Low threshold for hysteresis
    model_dir: models/snakers4_silero-vad
    min_silence_duration_ms: 1000  # If speaking pauses are long, you can set this value larger
    frame_window_threshold: 3  # Minimum frames with voice detection to trigger VAD (default: 3)
  
  TenVAD_ONNX:
    type: ten_vad_onnx
    model_path: models/ten-vad-onnx
    sample_rate: 16000
    hop_size: 256  # TEN VAD frame size (16ms at 16kHz)
    frame_size: 512  # Processing frame size
    threshold: 0.5  # High threshold for voice detection
    threshold_low: 0.2  # Low threshold for hysteresis
    min_silence_duration_ms: 1000  # Silence duration before ending speech
    frame_window_threshold: 3  # Minimum frames with voice detection to trigger VAD

LLM:
  # All openai types can modify hyperparameters, using AliLLM as example
  # Currently supported types are openai, dify, ollama, can adapt yourself
  AliLLM:
    # Define LLM API type
    type: openai
    # You can find your api_key here https://bailian.console.aliyun.com/?apiKey=1#/api-key
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    model_name: qwen-turbo
    api_key: your-deepseek-web-key
    temperature: 0.7  # Temperature value
    max_tokens: 500   # Maximum generation token count
    top_p: 1
    top_k: 50
    frequency_penalty: 0  # Frequency penalty
  AliAppLLM:
    # Define LLM API type
    type: AliBL
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    app_id: your-app-id
    # You can find your api_key here https://bailian.console.aliyun.com/?apiKey=1#/api-key
    api_key: your-api-key
    # Whether not to use local prompt: true|false (default don't use, please set prompt in Bailian application)
    is_no_prompt: true
    # Ali_memory_id: false (don't use)|your-memory-id (please get from Bailian application settings)
    # Tips!: Ali_memory doesn't implement multi-user storage memory (memory called by id)
    ali_memory_id: false
  DoubaoLLM:
    # Define LLM API type
    type: openai
    # First activate service, open the following URL, search for Doubao-1.5-pro in activated services and activate it
    # Activation address: https://console.volcengine.com/ark/region:ark+cn-beijing/openManagement?LLM=%7B%7D&OpenTokenDrawer=false
    # Free quota 500000 tokens
    # After activation, get key here: https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey?apikey=%7B%7D
    base_url: https://ark.cn-beijing.volces.com/api/v3
    model_name: doubao-1-5-pro-32k-250115
    api_key: your-doubao-web-key
  DeepSeekLLM:
    # Define LLM API type
    type: openai
    # You can find your api key here https://platform.deepseek.com/
    model_name: deepseek-chat
    url: https://api.deepseek.com
    api_key: your-deepseek-web-key
  ChatGLMLLM:
    # Define LLM API type
    type: openai
    # glm-4-flash is free, but still needs registration and api_key
    # You can find your api key here https://bigmodel.cn/usercenter/proj-mgmt/apikeys
    model_name: glm-4-flash
    url: https://open.bigmodel.cn/api/paas/v4/
    api_key: your-chat-glm-web-key
  OllamaLLM:
    # Define LLM API type
    type: ollama
    model_name: qwen2.5 #  Model name to use, need to download with ollama pull beforehand
    base_url: http://localhost:11434  # Ollama service address
  DifyLLM:
    # Define LLM API type
    type: dify
    # Recommend using locally deployed dify interface, some regions in China may have restricted access to dify public cloud interface
    # If using DifyLLM, prompt in config file is invalid, need to set prompt in dify console
    base_url: https://api.dify.ai/v1
    api_key: your-dify-llm-web-key
    # Conversation mode to use: can choose workflow workflows/run, conversation mode chat-messages, text generation completion-messages
    # When using workflows for return, input parameter is query, return parameter name should be set to answer
    # Text generation default input parameter is also query
    mode: chat-messages
  GeminiLLM:
    type: gemini
    # Google Gemini API, need to first create API key in Google Cloud console and get api_key
    # If using within China, please comply with "Interim Measures for the Management of Generative AI Services"
    # token application address: https://aistudio.google.com/apikey
    # If deployment location cannot access interface, need to enable VPN
    api_key: your-gemini-web-key
    model_name: "gemini-2.0-flash"
    http_proxy: ""  #"http://127.0.0.1:10808"
    https_proxy: "" #http://127.0.0.1:10808"
  CozeLLM:
    # Define LLM API type
    type: coze
    # You can find personal token here
    # https://www.coze.cn/open/oauth/pats
    # bot_id and user_id content should be written within quotes
    bot_id: "your-bot-id"
    user_id: "your-user-id"
    personal_access_token: your-coze-personal-token
  VolcesAiGatewayLLM:
    # Volcano Engine - Edge Large Model Gateway
    # Define LLM API type
    type: openai
    # First activate service, open the following URL, create gateway access key, search and check Doubao-pro-32k-functioncall, activate
    # If you need to use speech synthesis provided by edge large model gateway, also check Doubao-speech-synthesis, see TTS.VolcesAiGatewayTTS configuration
    # https://console.volcengine.com/vei/aigateway/
    # After activation, get key here: https://console.volcengine.com/vei/aigateway/tokens-list
    base_url: https://ai-gateway.vei.volces.com/v1
    model_name: doubao-pro-32k-functioncall
    api_key: your-gateway-access-key
  LMStudioLLM:
    # Define LLM API type
    type: openai
    model_name: deepseek-r1-distill-llama-8b@q4_k_m # Model name to use, need to download from community beforehand
    url: http://localhost:1234/v1 # LM Studio service address
    api_key: lm-studio # LM Studio service fixed API Key
  HomeAssistant:
    # Define LLM API type
    type: homeassistant
    base_url: http://homeassistant.local:8123
    agent_id: conversation.chatgpt
    api_key: your-home-assistant-api-access-token
  FastgptLLM:
    # Define LLM API type
    type: fastgpt
    # If using fastgpt, prompt in config file is invalid, need to set prompt in fastgpt console
    base_url: https://host/api/v1
    # You can find your api_key here
    # https://cloud.tryfastgpt.ai/account/apikey
    api_key: your-fastgpt-key
    variables:
      k: "v"
      k2: "v2"
  XinferenceLLM:
    # Define LLM API type
    type: xinference
    # Xinference service address and model name
    model_name: qwen2.5:72b-AWQ  # Model name to use, need to start corresponding model in Xinference beforehand
    base_url: http://localhost:9997  # Xinference service address
  XinferenceSmallLLM:
    # Define lightweight LLM API type for intent recognition
    type: xinference
    # Xinference service address and model name
    model_name: qwen2.5:3b-AWQ  # Small model name to use for intent recognition
    base_url: http://localhost:9997  # Xinference service address
# VLLM configuration (Vision Language Large Model)
VLLM:
  ChatGLMVLLM:
    type: openai
    # glm-4v-flash is Zhipu's free AI vision model, need to first create API key in Zhipu AI platform and get api_key
    # You can find your api key here https://bigmodel.cn/usercenter/proj-mgmt/apikeys
    model_name: glm-4v-flash  # Zhipu AI vision model
    url: https://open.bigmodel.cn/api/paas/v4/
    api_key: your-api-key
  QwenVLVLLM:
    type: openai
    model_name: qwen2.5-vl-3b-instruct
    url: https://dashscope.aliyuncs.com/compatible-mode/v1
    # You can find your api key here https://bailian.console.aliyun.com/?apiKey=1#/api-key
    api_key: your-api-key
TTS:
  # Currently supported types are edge, doubao, can adapt yourself
  EdgeTTS:
    # Define TTS API type
    type: edge
    voice: zh-CN-XiaoxiaoNeural
    output_dir: tmp/
  DoubaoTTS:
    # Define TTS API type
    type: doubao
    # Volcano Engine Speech Synthesis Service, need to first create application in Volcano Engine console and get appid and access_token
    # Volcano Engine Speech must be purchased, starting price 30 yuan, gives 100 concurrency. If using free version, only 2 concurrency, will often report tts errors
    # After purchasing service and free voices, may need to wait about half hour before use.
    # Regular voices activate here: https://console.volcengine.com/speech/service/8
    # Taiwan Xiaohe voice activate here: https://console.volcengine.com/speech/service/10007, after activation set voice below to zh_female_wanwanxiaohe_moon_bigtts
    api_url: https://openspeech.bytedance.com/api/v1/tts
    voice: BV001_streaming
    output_dir: tmp/
    authorization: "Bearer;"
    appid: your-volcano-engine-speech-synthesis-service-appid
    access_token: your-volcano-engine-speech-synthesis-service-access-token
    cluster: volcano_tts
    speed_ratio: 1.0
    volume_ratio: 1.0
    pitch_ratio: 1.0
  #Volcano tts, supports bidirectional streaming tts
  HuoshanDoubleStreamTTS:
    type: huoshan_double_stream
    # Visit https://console.volcengine.com/speech/service/10007 to activate speech synthesis large model, purchase voices
    # Get appid and access_token at bottom of page
    # Resource ID is fixed: volc.service_type.10029 (Large model speech synthesis and mixing)
    # If using Gizwits, change interface address to wss://bytedance.gizwitsapi.com/api/v3/tts/bidirection
    # Gizwits doesn't need to fill appid
    ws_url: wss://openspeech.bytedance.com/api/v3/tts/bidirection
    appid: your-volcano-engine-speech-synthesis-service-appid
    access_token: your-volcano-engine-speech-synthesis-service-access-token
    resource_id: volc.service_type.10029
    speaker: zh_female_wanwanxiaohe_moon_bigtts
  CosyVoiceSiliconflow:
    type: siliconflow
    # SiliconFlow TTS
    # token application address https://cloud.siliconflow.cn/account/ak
    model: FunAudioLLM/CosyVoice2-0.5B
    voice: FunAudioLLM/CosyVoice2-0.5B:alex
    output_dir: tmp/
    access_token: your-siliconflow-api-key
    response_format: wav
  CozeCnTTS:
    type: cozecn
    # COZECN TTS
    # token application address https://www.coze.cn/open/oauth/pats
    voice: 7426720361733046281
    output_dir: tmp/
    access_token: your-coze-web-key
    response_format: wav
  VolcesAiGatewayTTS:
    type: openai
    # Volcano Engine - Edge Large Model Gateway
    # First activate service, open the following URL, create gateway access key, search and check Doubao-speech-synthesis, activate
    # If you need to use LLM provided by edge large model gateway, also check Doubao-pro-32k-functioncall, see LLM.VolcesAiGatewayLLM configuration
    # https://console.volcengine.com/vei/aigateway/
    # After activation, get key here: https://console.volcengine.com/vei/aigateway/tokens-list
    api_key: your-gateway-access-key
    api_url: https://ai-gateway.vei.volces.com/v1/audio/speech
    model: doubao-tts
    # Voice list see https://www.volcengine.com/docs/6561/1257544
    voice: zh_male_shaonianzixin_moon_bigtts
    speed: 1
    output_dir: tmp/
  FishSpeech:
    # Follow tutorial: https://github.com/xinnan-tech/xiaozhi-esp32-server/blob/main/docs/fish-speech-integration.md
    type: fishspeech
    output_dir: tmp/
    response_format: wav
    reference_id: null
    reference_audio: ["config/assets/wakeup_words.wav",]
    reference_text: ["Hello, I'm Xiaozhi, a Taiwanese girl with a nice voice. So happy to meet you! What have you been up to lately? Don't forget to give me some interesting gossip, I love hearing gossip!",]
    normalize: true
    max_new_tokens: 1024
    chunk_length: 200
    top_p: 0.7
    repetition_penalty: 1.2
    temperature: 0.7
    streaming: false
    use_memory_cache: "on"
    seed: null
    channels: 1
    rate: 44100
    api_key: "your-api-key"
    api_url: "http://127.0.0.1:8080/v1/tts"
  GPT_SOVITS_V2:
    # Define TTS API type
    # TTS startup method:
    # python api_v2.py -a 127.0.0.1 -p 9880 -c GPT_SoVITS/configs/demo.yaml
    type: gpt_sovits_v2
    url: "http://127.0.0.1:9880/tts"
    output_dir: tmp/
    text_lang: "auto"
    ref_audio_path: "demo.wav"
    prompt_text: ""
    prompt_lang: "zh"
    top_k: 5
    top_p: 1
    temperature: 1
    text_split_method: "cut0"
    batch_size: 1
    batch_threshold: 0.75
    split_bucket: true
    return_fragment: false
    speed_factor: 1.0
    streaming_mode: false
    seed: -1
    parallel_infer: true
    repetition_penalty: 1.35
    aux_ref_audio_paths: []
  GPT_SOVITS_V3:
    # Define TTS API type GPT-SoVITS-v3lora-********
    # TTS startup method:
    # python api.py
    type: gpt_sovits_v3
    url: "http://127.0.0.1:9880"
    output_dir: tmp/
    text_language: "auto"
    refer_wav_path: "caixukun.wav"
    prompt_language: "zh"
    prompt_text: ""
    top_k: 15
    top_p: 1.0
    temperature: 1.0
    cut_punc: ""
    speed: 1.0
    inp_refs: []
    sample_steps: 32
    if_sr: false
  MinimaxTTS:
    # Minimax Speech Synthesis Service, need to first create account and recharge on minimax platform, and obtain login information
    # Platform address: https://platform.minimaxi.com/
    # Recharge address: https://platform.minimaxi.com/user-center/payment/balance
    # group_id address: https://platform.minimaxi.com/user-center/basic-information
    # api_key address: https://platform.minimaxi.com/user-center/basic-information/interface-key
    # Define TTS API type
    type: minimax
    output_dir: tmp/
    group_id: your-minimax-platform-group-id
    api_key: your-minimax-platform-interface-key
    model: "speech-01-turbo"
    # This setting will take priority over voice_id setting in voice_setting; if neither is set, defaults to female-shaonv
    voice_id: "female-shaonv"
    # The following can be left unset, using default settings
    # voice_setting:
    #     voice_id: "male-qn-qingse"
    #     speed: 1
    #     vol: 1
    #     pitch: 0
    #     emotion: "happy"
    # pronunciation_dict:
    #     tone:
    #       - "process/(chu3)(li3)"
    #       - "danger/dangerous"
    # audio_setting:
    #     sample_rate: 32000
    #     bitrate: 128000
    #     format: "mp3"
    #     channel: 1
    # timber_weights:
    #   -
    #     voice_id: male-qn-qingse
    #     weight: 1
    #   -
    #     voice_id: female-shaonv
    #     weight: 1
    # language_boost: auto
  AliyunTTS:
    # Alibaba Cloud Intelligent Speech Interaction Service, need to first activate service on Alibaba Cloud platform, then obtain verification information
    # Platform address: https://nls-portal.console.aliyun.com/
    # appkey address: https://nls-portal.console.aliyun.com/applist
    # token address: https://nls-portal.console.aliyun.com/overview
    # Define TTS API type
    type: aliyun
    output_dir: tmp/
    appkey: your-alibaba-cloud-intelligent-speech-interaction-service-project-appkey
    token: your-alibaba-cloud-intelligent-speech-interaction-service-accesstoken-temporary-24hours-for-long-term-use-access-key-id-access-key-secret-below
    voice: xiaoyun
    access_key_id: your-alibaba-cloud-account-access-key-id
    access_key_secret: your-alibaba-cloud-account-access-key-secret

    # The following can be left unset, using default settings
    # format: wav
    # sample_rate: 16000
    # volume: 50
    # speech_rate: 0
    # pitch_rate: 0
    # Add 302.ai TTS configuration
    # token application address: https://dash.302.ai/
  TencentTTS:
    # Tencent Cloud Intelligent Speech Interaction Service, need to first activate service on Tencent Cloud platform
    # appid, secret_id, secret_key application address: https://console.cloud.tencent.com/cam/capi
    # Free resource collection: https://console.cloud.tencent.com/tts/resourcebundle
    type: tencent
    output_dir: tmp/
    appid: your-tencent-cloud-appid
    secret_id: your-tencent-cloud-secret-id
    secret_key: your-tencent-cloud-secret-key
    region: ap-guangzhou
    voice: 101001

  TTS302AI:
    # 302AI Speech Synthesis Service, need to first create account and recharge on 302 platform, and obtain key information
    # Get api_key path: https://dash.302.ai/apis/list
    # Price: $35/million characters. Original Volcano ¥450 yuan/million characters
    type: doubao
    api_url: https://api.302ai.cn/doubao/tts_hd
    authorization: "Bearer "
    # Taiwan Xiaohe voice
    voice: "zh_female_wanwanxiaohe_moon_bigtts"
    output_dir: tmp/
    access_token: "your-302-api-key"
  GizwitsTTS:
    type: doubao
    # Volcano Engine as base, can fully use enterprise-grade Volcano Engine speech synthesis service
    # First 10,000 registered users will receive 5 yuan experience credit
    # Get API Key address: https://agentrouter.gizwitsapi.com/panel/token
    api_url: https://bytedance.gizwitsapi.com/api/v1/tts
    authorization: "Bearer "
    # Taiwan Xiaohe voice
    voice: "zh_female_wanwanxiaohe_moon_bigtts"
    output_dir: tmp/
    access_token: "your-gizwits-api-key"
  ACGNTTS:
    # Online website: https://acgn.ttson.cn/
    # token purchase: www.ttson.cn
    # For development questions please submit to QQ on website
    # Character id acquisition address: ctrl+f quick search character——website administrator doesn't allow publishing, can ask website administrator
    # Parameter meanings see development documentation: https://www.yuque.com/alexuh/skmti9/wm6taqislegb02gd?singleDoc#
    type: ttson
    token: your_token
    voice_id: 1695
    speed_factor: 1
    pitch_factor: 0
    volume_change_dB: 0
    to_lang: ZH
    url: https://u95167-bd74-2aef8085.westx.seetacloud.com:8443/flashsummary/tts?token=
    format: mp3
    output_dir: tmp/
    emotion: 1
  OpenAITTS:
    # OpenAI official text-to-speech service, supports most languages worldwide
    type: openai
    # You can get api key here
    # https://platform.openai.com/api-keys
    api_key: your-openai-api-key
    # Domestic use requires proxy
    api_url: https://api.openai.com/v1/audio/speech
    # Optional tts-1 or tts-1-hd, tts-1 faster tts-1-hd better quality
    model: tts-1
    # Speaker, options: alloy, echo, fable, onyx, nova, shimmer
    voice: onyx
    # Speed range 0.25-4.0
    speed: 1
    output_dir: tmp/
  CustomTTS:
    # Custom TTS interface service, request parameters can be customized, can integrate with many TTS services
    # Example with locally deployed KokoroTTS
    # If only CPU: docker run -p 8880:8880 ghcr.io/remsky/kokoro-fastapi-cpu:latest
    # If only GPU: docker run --gpus all -p 8880:8880 ghcr.io/remsky/kokoro-fastapi-gpu:latest
    # Requires interface to use POST method and return audio file
    type: custom
    method: POST
    url: "http://127.0.0.1:8880/v1/audio/speech"
    params: # Custom request parameters
      input: "{prompt_text}"
      response_format: "mp3"
      download_format: "mp3"
      voice: "zf_xiaoxiao"
      lang_code: "z"
      return_download_link: true
      speed: 1
      stream: false
    headers: # Custom request headers
      # Authorization: Bearer xxxx
    format: mp3 # Audio format returned by interface
    output_dir: tmp/
  LinkeraiTTS:
    type: linkerai
    api_url: https://tts.linkerai.cn/tts
    audio_format: "pcm"
    # Default access_token is for free testing use, please don't use this access_token for commercial purposes
    # If results are good, you can apply for your own token, application address: https://linkerai.cn
    # Parameter meanings see development documentation: https://tts.linkerai.cn/docs
    # Supports voice cloning, can upload your own audio, fill in voice parameter, when voice parameter is empty, uses default voice
    access_token: "U4YdYXVfpwWnk2t5Gp822zWPCuORyeJL"
    voice: "OUeAo1mhq6IBExi"
    output_dir: tmp/