Open Source License

The ten-vad is licensed pursuant to the Apache License v2.0, with the
following additional conditions. You may reproduce, prepare Derivative Works
of, publicly display, publicly perform, sublicense, distribute, or otherwise
make available (together, "Deploy") the ten-vad, for commercial or
non-commercial purposes, provided that you agree to abide by the terms below:

    1. You may not Deploy the ten-vad in a way that competes with Agora's
       offerings and/or that allows others to compete with Agora's offerings,
       including without limitation enabling any third party to develop or
       deploy Applications.
    
    2. You may Deploy the ten-vad solely to create and enable deployment
       of your Application(s) solely for your benefit and the benefit of your
       direct End Users. If you prefer, you may include the following notice in
       the documentation of your Application(s): "Powered by ten-vad".

    3. Derivative Works of the ten-vad remain subject to this Open Source
       License.

    4. "End Users" shall mean the end-users of your Application(s) who access
       the ten-vad solely to the extent necessary to access and use the
       Application(s) you create or deploy using ten-vad.

    5. "Application(s)" shall mean your software programs designed or developed
       by using the ten-vad or where deployment is enabled by the ten-vad.

  Copyright © 2025 Agora

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.

=======================================================================================

  Note that the project contains derived code from other open source project
  with BSD-3-Clause and BSD-2-Clause license, refer to the "NOTICES" 
  file in the root directory for detailed information.
