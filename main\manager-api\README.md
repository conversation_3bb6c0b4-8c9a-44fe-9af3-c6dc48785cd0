This is a development document. If you need to deploy the Xiaozhi server, [click here to view the deployment tutorial](../../README.md#%E9%83%A8%E7%BD%B2%E6%96%87%E6%A1%A3)

# Project Introduction

manager-api: This project is developed based on the SpringBoot framework.

When developing, use a code editor and select the `manager-api` folder as the project directory when importing the project.

# Development Environment
JDK 21
Maven 3.8+
MySQL 8.0+
Redis 5.0+
Vue 3.x

# API Documentation
After starting, open: http://localhost:8002/xiaozhi/doc.html

