knife4j:
  production: false
  enable: true
  basic:
    enable: false
    username: renren
    password: 2ZABCDEUgF
  setting:
    enableFooter: false
spring:
  datasource:
    druid:
      #MySQL
      # driver-class-name: com.mysql.cj.jdbc.Driver
      # # Railway MySQL Configuration - Updated for Railway database
      # url: *********************************************************************************************************************************************************************************************
      # username: root
      # password: cTefiuLmZSMqGpcQImUmBIsUBhNyUvrK
      driver-class-name: com.mysql.cj.jdbc.Driver
      # Railway MySQL Configuration - Updated for Railway database
      url: *******************************************************************************************************************************************************************************************
      username: root
      password: OcaVNLKcwNdyElfaeUPqvvfYZiiEHgdm

      
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: false
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # Railway Redis Configuration
      host: caboose.proxy.rlwy.net    # Railway Redis服务器地址
      port: 45879                     # Railway Redis服务器连接端口
      password: mJwoNsjrRcbAkElEWATNOxmqUNraCKIT  # Railway Redis服务器连接密码
      username: default               # Railway Redis用户名
      database: 0                     # Redis数据库索引（默认为0）
      timeout: 30000ms                # 连接超时时间（毫秒）- 增加超时时间
      ssl:
        enabled: false                # 尝试禁用SSL，Railway可能不需要
      lettuce:
        pool:
          max-active: 8               # 连接池最大连接数（使用负值表示没有限制）
          max-idle: 8                 # 连接池中的最大空闲连接
          min-idle: 0                 # 连接池中的最小空闲连接
        shutdown-timeout: 100ms       # 客户端优雅关闭的等待时间
